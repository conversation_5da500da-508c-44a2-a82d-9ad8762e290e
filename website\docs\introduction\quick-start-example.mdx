---
id: quick-start-example
title: Quick Start Examples
sidebar_label: Quick Start Examples
sidebar_position: 5
sidebar_class_name: docs-sidebar-quick-start
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import ComponentPreview from '@site/src/components/ui/component-preview';
import Preview from '@site/src/components/cloc-sdk/preview';

# Quick Start Examples

This guide provides comprehensive examples for getting started with the Cloc SDK, from basic timer components to advanced analytics and tracking features.

:::tip Prerequisites
- React 18+
- TypeScript 4.5+
- Node.js 18+
:::

## Installation

Install the packages you need for your project:

<Tabs>
<TabItem value="npm" label="npm" default>

```bash
# Core package for time tracking, reports and analytics
npm install @cloc/atoms

# Add tracking package
npm install @cloc/tracking
```
</TabItem>
<TabItem value="yarn" label="yarn">

```bash
# Core package for time tracking, reports and analytics
yarn add @cloc/atoms

# Add tracking package
yarn add @cloc/tracking
```
</TabItem>

<TabItem value="pnpm" label="pnpm">

```bash
# Core package for time tracking, reports and analytics
pnpm add @cloc/atoms

# Add tracking package
pnpm add @cloc/tracking
```
</TabItem>

</Tabs>

## Quick Setup

### 1. Basic Provider Setup

Every Cloc application starts with the `ClocProvider` wrapper:

```tsx
import { ClocProvider } from "@cloc/atoms";
import "@cloc/atoms/styles.css";

export default function App() {
  return (
    <ClocProvider>
      {/* Your app components */}
    </ClocProvider>
  );
}
```

### 2. Simple Timer Example

Get started with a basic timer in just a few lines:

<ComponentPreview code={`import { ModernCloc, ClocProvider } from "@cloc/atoms";
export default function SimpleTimer() {
	return (
		<ClocProvider>
			<ModernCloc
				expanded={false}
				showProgress={true}					
				variant="default"
			/>
		</ClocProvider>
	);
}`} language='tsx' >

<Preview />
</ComponentPreview>

## Core Timer Components

### ModernCloc - Advanced Timer

The flagship timer component with modern design and advanced features:

<ComponentPreview code={`
import { ModernCloc } from "@cloc/atoms";

function ModernTimerExamples() {
  return (
    <div className="space-y-6">
      {/* Compact Timer */}
      <ModernCloc
        expanded={false}
        showProgress={true}
        variant="default"
        size="sm"
      />

      {/* Expanded Timer with Controls */}
      <ModernCloc
        expanded={true}
        showProgress={true}
        variant="bordered"
        size="default"
        resizable={true}
      />

      {/* Large Resizable Timer */}
      <ModernCloc
        expanded={true}
        showProgress={true}
        variant="default"
        size="lg"
        resizable={true}
        draggable={true}
      />
    </div>
  );
}

`} language='tsx'>

<Preview />
</ComponentPreview>


**ModernCloc Props:**
- `expanded`: Toggle between compact and expanded views
- `showProgress`: Display a progress indicator
- `variant`: "default" | "bordered" styles
- `size`: "sm" | "default" | "lg" sizes
- `resizable`: Allow users to resize the component
- `draggable`: Enable drag functionality
- `separator`: Custom time separator (default: ":")

### ClocBasic - Essential Timer

A lightweight timer component for basic time tracking needs:

```tsx
import { ClocBasic } from "@cloc/atoms";

function BasicTimerExamples() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Default Basic Timer */}
      <ClocBasic
        readonly={false}
        progress={false}
      />

      {/* Timer with Progress */}
      <ClocBasic
        readonly={false}
        progress={true}
        className="w-fit max-w-[300px]"
      />

      {/* Read-only Display */}
      <ClocBasic
        readonly={true}
        progress={false}
      />
    </div>
  );
}
```

### BasicTimer - Customizable Timer

Highly customizable timer with styling options:

```tsx
import { BasicTimer } from "@cloc/atoms";

function CustomTimerExamples() {
  return (
    <div className="space-y-4">
      {/* Primary Theme Timer */}
      <BasicTimer
        background="primary"
        color="secondary"
        border="thick"
        icon={true}
      />

      {/* Secondary Theme Timer */}
      <BasicTimer
        background="secondary"
        color="primary"
        border="thin"
        icon={false}
      />

      {/* Destructive Theme Timer */}
      <BasicTimer
        background="destructive"
        color="primary"
        border="none"
        readonly={true}
      />
    </div>
  );
}
```

## Using Hooks for Custom Components

### useClocContext Hook

Access timer state and controls in custom components:

```tsx
import { useClocContext } from "@cloc/atoms";

function CustomTimerDisplay() {
  const {
    hours,
    minutes,
    seconds,
    isRunning,
    startTimer,
    stopTimer,
    todayTrackedTime
  } = useClocContext();

  return (
    <div className="p-4 border rounded-lg">
      <div className="text-2xl font-mono">
        {String(hours).padStart(2, '0')}:
        {String(minutes).padStart(2, '0')}:
        {String(seconds).padStart(2, '0')}
      </div>

      <div className="mt-2 space-x-2">
        <button
          onClick={isRunning ? stopTimer : startTimer}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          {isRunning ? 'Stop' : 'Start'}
        </button>
      </div>

      <div className="mt-2 text-sm text-gray-600">
        Today: {todayTrackedTime.hours}h {todayTrackedTime.minutes}m
      </div>
    </div>
  );
}
```

## Analytics & Tracking Integration

### Setting Up Tracking

Initialize tracking to capture user interactions and analytics:

```tsx
import { tracker } from "@cloc/tracking";
import { TrackingProvider } from "@cloc/atoms";

// Initialize tracking with your credentials
tracker.start({
  organizationId: 'your-org-id',
  tenantId: 'your-tenant-id',
  token: 'your-auth-token'
});

function AppWithTracking() {
  return (
    <TrackingProvider>
      <ClocProvider>
        {/* Your app components */}
      </ClocProvider>
    </TrackingProvider>
  );
}
```

### Analytics Dashboard

Create a comprehensive analytics dashboard:

```tsx
import {
  TrackingProvider,
  ClocTrackingFilter,
  ClocTrackingHeatmap,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight
} from "@cloc/atoms";

function AnalyticsDashboard() {
  return (
    <TrackingProvider>
      <div className="space-y-6 p-6">
        {/* Filter Controls */}
        <ClocTrackingFilter
          autoRefresh={true}
          refreshInterval={30000}
        />

        {/* Analytics Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ClocTrackingClickInsight className="w-full" />
          <ClocTrackingSessionInsight className="w-full" />
        </div>

        {/* Heatmap Visualization */}
        <ClocTrackingHeatmap
          className="w-full h-[600px]"
          showControl={true}
        />
      </div>
    </TrackingProvider>
  );
}
```

### Click Analytics Component

Detailed click analytics with insights:

```tsx
import { ClocTrackingClickInsight } from "@cloc/atoms";

function ClickAnalytics() {
  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4">Click Analytics</h2>
      <ClocTrackingClickInsight
        className="w-full"
        showElementDetails={true}
      />
    </div>
  );
}
```

### Session Analytics Component

Monitor user session metrics and engagement:

```tsx
import { ClocTrackingSessionInsight } from "@cloc/atoms";

function SessionAnalytics() {
  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4">Session Insights</h2>
      <ClocTrackingSessionInsight className="w-full" />
    </div>
  );
}
```

## Data Visualization & Reports

### BasicClocReport - Comprehensive Charts

Create powerful data visualizations with multiple chart types:

```tsx
import { BasicClocReport } from "@cloc/atoms";

function ReportDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Bar Charts */}
      <BasicClocReport
        type="bar-vertical"
        variant="default"
        size="default"
      />

      <BasicClocReport
        type="bar"
        variant="bordered"
        size="lg"
      />

      {/* Time Series Charts */}
      <BasicClocReport
        type="line"
        variant="default"
        size="default"
      />

      <BasicClocReport
        type="area"
        variant="default"
        size="default"
      />

      {/* Advanced Visualizations */}
      <BasicClocReport
        type="radar"
        variant="bordered"
        size="sm"
      />

      <BasicClocReport
        type="pie"
        variant="default"
        size="default"
      />
    </div>
  );
}
```

**Available Chart Types:**
- `bar-vertical` & `bar`: Compare values across categories
- `line`: Show trends over time
- `area`: Display cumulative data
- `radar`: Multi-dimensional data visualization
- `pie`: Distribution analysis
- `radial`: Circular progress visualization
- `tooltip`: Interactive charts with detailed information

### Custom Chart Configuration

Configure charts with custom data and styling:

```tsx
import { BasicClocReport, useClocContext } from "@cloc/atoms";

function CustomChart() {
  const { config, setConfig } = useClocContext();

  const customConfig = {
    ...config,
    colors: ['#3b82f6', '#ef4444', '#10b981'],
    theme: 'dark'
  };

  return (
    <div className="p-6">
      <BasicClocReport
        type="line"
        variant="bordered"
        size="lg"
      />
    </div>
  );
}
```

## Real-World Use Cases

### E-commerce Time Tracking

Track time spent on product development and customer support:

```tsx
import { ModernCloc, ClocProvider, tracker } from "@cloc/atoms";
import { useEffect } from "react";

function EcommerceTimeTracker() {
  useEffect(() => {
    // Initialize tracking for e-commerce analytics
    tracker.start({
      organizationId: 'ecommerce-org-123',
      tenantId: 'tenant-456',
      token: process.env.CLOC_TOKEN
    });
  }, []);

  return (
    <ClocProvider>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Product Development Timer</h1>

        {/* Development Time Tracker */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 border rounded-lg">
            <h3 className="font-semibold mb-3">Frontend Development</h3>
            <ModernCloc
              expanded={true}
              showProgress={true}
              variant="bordered"
              size="default"
            />
          </div>

          <div className="p-4 border rounded-lg">
            <h3 className="font-semibold mb-3">Backend Development</h3>
            <ModernCloc
              expanded={true}
              showProgress={true}
              variant="default"
              size="default"
            />
          </div>
        </div>
      </div>
    </ClocProvider>
  );
}
```

### User Analytics Dashboard

Monitor user behavior and productivity metrics:

```tsx
import {
  TrackingProvider,
  ClocTrackingFilter,
  ClocTrackingClickInsight,
  ClocTrackingSessionInsight,
  ClocTrackingHeatmap
} from "@cloc/atoms";

function UserAnalyticsDashboard() {
  return (
    <TrackingProvider>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">User Analytics Dashboard</h1>

          {/* Filter Section */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <ClocTrackingFilter
              autoRefresh={true}
              refreshInterval={60000}
            />
          </div>

          {/* Analytics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Click Analytics</h2>
              <ClocTrackingClickInsight />
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Session Insights</h2>
              <ClocTrackingSessionInsight />
            </div>
          </div>

          {/* Heatmap Section */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">User Interaction Heatmap</h2>
            <ClocTrackingHeatmap
              className="w-full h-[600px]"
              showControl={true}
            />
          </div>
        </div>
      </div>
    </TrackingProvider>
  );
}
```
### A/B Testing with Timer Components

Test different timer configurations to optimize user engagement:

```tsx
import { ModernCloc, ClocBasic } from "@cloc/atoms";
import { useState, useEffect } from "react";

function ABTestTimers() {
  const [variant, setVariant] = useState<'A' | 'B'>('A');

  useEffect(() => {
    // Randomly assign users to A/B test groups
    setVariant(Math.random() > 0.5 ? 'A' : 'B');
  }, []);

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-4">
        Timer Variant: {variant}
      </h2>

      {variant === 'A' ? (
        // Variant A: Modern expanded timer
        <ModernCloc
          expanded={true}
          showProgress={true}
          variant="bordered"
          size="lg"
        />
      ) : (
        // Variant B: Compact basic timer
        <ClocBasic
          progress={true}
          readonly={false}
        />
      )}
    </div>
  );
}
```

### Productivity Monitoring

Track team productivity with comprehensive time analytics:

```tsx
import {
  ModernCloc,
  BasicClocReport,
  useClocContext,
  ClocProvider
} from "@cloc/atoms";

function ProductivityMonitor() {
  const { todayTrackedTime, isRunning } = useClocContext();

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Active Timer */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="font-semibold mb-4">Current Session</h3>
            <ModernCloc
              expanded={true}
              showProgress={true}
              variant="default"
              size="default"
            />

            <div className="mt-4 p-3 bg-gray-50 rounded">
              <p className="text-sm text-gray-600">
                Status: {isRunning ? 'Active' : 'Paused'}
              </p>
              <p className="text-sm text-gray-600">
                Today: {todayTrackedTime.hours}h {todayTrackedTime.minutes}m
              </p>
            </div>
          </div>
        </div>

        {/* Analytics Charts */}
        <div className="lg:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BasicClocReport type="line" size="sm" />
            <BasicClocReport type="bar-vertical" size="sm" />
            <BasicClocReport type="area" size="sm" />
            <BasicClocReport type="pie" size="sm" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Wrap with provider
function ProductivityApp() {
  return (
    <ClocProvider>
      <ProductivityMonitor />
    </ClocProvider>
  );
}
```

## Theme Support & Customization

### Dark/Light Theme Toggle

Cloc supports automatic theme switching:

```tsx
import { ClocProvider, ClocThemeToggle, ModernCloc } from "@cloc/atoms";

function ThemedApp() {
  return (
    <ClocProvider defaultTheme="light">
      <div className="min-h-screen bg-background text-foreground">
        {/* Theme Toggle Button */}
        <div className="p-4 flex justify-end">
          <ClocThemeToggle />
        </div>

        {/* Components automatically adapt to theme */}
        <div className="p-6 space-y-6">
          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="bordered"
          />

          <BasicClocReport type="line" variant="default" />
        </div>
      </div>
    </ClocProvider>
  );
}
```

### Custom Theme Configuration

Create custom themes for your application:

```tsx
import { ClocProvider, ModernCloc } from "@cloc/atoms";
import { ThemeUIProvider } from "theme-ui";

const customTheme = {
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#f8fafc',
    surface: '#ffffff',
    text: '#1e293b'
  },
  fonts: {
    body: 'Inter, sans-serif',
    heading: 'Inter, sans-serif',
    monospace: 'JetBrains Mono, monospace'
  }
};

function CustomThemedApp() {
  return (
    <ThemeUIProvider theme={customTheme}>
      <ClocProvider>
        <ModernCloc
          expanded={true}
          showProgress={true}
          variant="default"
        />
      </ClocProvider>
    </ThemeUIProvider>
  );
}
```
## Framework Integrations

### Next.js Integration

Complete Next.js setup with SSR support:

```tsx
// pages/_app.tsx
import type { AppProps } from 'next/app';
import { ClocProvider } from '@cloc/atoms';
import { TrackingProvider } from '@cloc/atoms';
import { tracker } from '@cloc/tracking';
import '@cloc/atoms/styles.css';

// Initialize tracking
if (typeof window !== 'undefined') {
  tracker.start({
    organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID!,
    tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID!,
    token: process.env.NEXT_PUBLIC_CLOC_TOKEN!
  });
}

export default function App({ Component, pageProps }: AppProps) {
  return (
    <TrackingProvider>
      <ClocProvider>
        <Component {...pageProps} />
      </ClocProvider>
    </TrackingProvider>
  );
}
```

```tsx
// pages/dashboard.tsx
import { ModernCloc, ClocTrackingHeatmap } from '@cloc/atoms';
import { GetServerSideProps } from 'next';

interface DashboardProps {
  userToken: string;
}

export default function Dashboard({ userToken }: DashboardProps) {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Time Tracking Dashboard</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Active Timer</h2>
          <ModernCloc
            expanded={true}
            showProgress={true}
            variant="bordered"
          />
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">User Analytics</h2>
          <ClocTrackingHeatmap className="h-[400px]" />
        </div>
      </div>
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  // Server-side authentication logic
  const userToken = context.req.cookies.authToken || '';

  return {
    props: {
      userToken
    }
  };
};
```

### Builder.io Integration

Register Cloc components for visual page building:

```tsx
import { Builder } from "@builder.io/react";
import { ModernCloc, ClocBasic, BasicClocReport } from "@cloc/atoms";

// Register ModernCloc component
Builder.registerComponent(ModernCloc, {
  name: "Modern Timer",
  inputs: [
    { name: "expanded", type: "boolean", defaultValue: false },
    { name: "showProgress", type: "boolean", defaultValue: true },
    { name: "variant", type: "string", enum: ["default", "bordered"], defaultValue: "default" },
    { name: "size", type: "string", enum: ["sm", "default", "lg"], defaultValue: "default" },
    { name: "resizable", type: "boolean", defaultValue: false },
    { name: "draggable", type: "boolean", defaultValue: false }
  ],
});

// Register ClocBasic component
Builder.registerComponent(ClocBasic, {
  name: "Basic Timer",
  inputs: [
    { name: "readonly", type: "boolean", defaultValue: false },
    { name: "progress", type: "boolean", defaultValue: false }
  ],
});

// Register BasicClocReport component
Builder.registerComponent(BasicClocReport, {
  name: "Cloc Report Chart",
  inputs: [
    {
      name: "type",
      type: "string",
      enum: ["bar", "bar-vertical", "line", "area", "pie", "radar", "radial", "tooltip"],
      defaultValue: "bar-vertical"
    },
    { name: "variant", type: "string", enum: ["default", "bordered"], defaultValue: "default" },
    { name: "size", type: "string", enum: ["sm", "default", "lg"], defaultValue: "default" }
  ],
});
```

### React Native Integration (Universal Package)

Use Cloc components in React Native applications:

```tsx
import { ClocProviderUniversal, ModernCloc } from '@cloc/atoms/universal';
import { View, Text } from 'react-native';

const config = {
  token: 'your-auth-token',
  apiUrl: 'https://api.cloc.ai',
  environment: 'production' as const
};

export default function MobileApp() {
  return (
    <ClocProviderUniversal config={config}>
      <View style={{ flex: 1, padding: 20 }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
          Mobile Time Tracker
        </Text>

        <ModernCloc
          expanded={true}
          showProgress={true}
          variant="default"
          size="default"
        />
      </View>
    </ClocProviderUniversal>
  );
}
```

## Advanced Examples

### Custom Hook Integration

Create custom hooks for specific business logic:

```tsx
import { useClocContext, useReport } from '@cloc/atoms';
import { useState, useEffect } from 'react';

function useProductivityMetrics() {
  const { todayTrackedTime, isRunning } = useClocContext();
  const [productivity, setProductivity] = useState(0);

  useEffect(() => {
    // Calculate productivity score based on tracked time
    const totalMinutes = todayTrackedTime.hours * 60 + todayTrackedTime.minutes;
    const targetMinutes = 8 * 60; // 8 hours target
    const score = Math.min((totalMinutes / targetMinutes) * 100, 100);
    setProductivity(score);
  }, [todayTrackedTime]);

  return {
    productivity,
    isActive: isRunning,
    todayHours: todayTrackedTime.hours,
    todayMinutes: todayTrackedTime.minutes
  };
}

function ProductivityWidget() {
  const { productivity, isActive, todayHours, todayMinutes } = useProductivityMetrics();

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h3 className="font-semibold mb-2">Today's Productivity</h3>
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${productivity}%` }}
            />
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {productivity.toFixed(1)}% of daily goal
          </p>
        </div>
        <div className="text-right">
          <p className="font-mono text-lg">
            {todayHours}h {todayMinutes}m
          </p>
          <p className="text-sm text-gray-600">
            {isActive ? 'Active' : 'Paused'}
          </p>
        </div>
      </div>
    </div>
  );
}
```
### TypeScript Integration

Leverage full TypeScript support for type safety:

```tsx
import {
  ModernCloc,
  ClocProvider,
  useClocContext,
  IModernClocProps,
  IClocContext
} from '@cloc/atoms';
import { tracker, IClocConfig } from '@cloc/tracking';

// Type-safe configuration
const trackingConfig: IClocConfig = {
  organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID!,
  tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID!,
  token: process.env.NEXT_PUBLIC_CLOC_TOKEN!
};

// Type-safe component props
interface TimerDashboardProps {
  userId: string;
  projectId?: string;
  onTimerStart?: () => void;
  onTimerStop?: () => void;
}

const TimerDashboard: React.FC<TimerDashboardProps> = ({
  userId,
  projectId,
  onTimerStart,
  onTimerStop
}) => {
  const clocContext: IClocContext = useClocContext();

  const timerProps: IModernClocProps = {
    expanded: true,
    showProgress: true,
    variant: 'bordered',
    size: 'default',
    resizable: false,
    draggable: false
  };

  React.useEffect(() => {
    tracker.start(trackingConfig);
  }, []);

  return (
    <div className="p-6">
      <ModernCloc {...timerProps} />

      <div className="mt-4 p-4 bg-gray-50 rounded">
        <p>User: {userId}</p>
        {projectId && <p>Project: {projectId}</p>}
        <p>Status: {clocContext.isRunning ? 'Running' : 'Stopped'}</p>
      </div>
    </div>
  );
};

export default function TypeSafeApp() {
  return (
    <ClocProvider>
      <TimerDashboard
        userId="user-123"
        projectId="project-456"
        onTimerStart={() => console.log('Timer started')}
        onTimerStop={() => console.log('Timer stopped')}
      />
    </ClocProvider>
  );
}
```

### Error Handling & Loading States

Implement robust error handling and loading states:

```tsx
import {
  ModernCloc,
  ClocProvider,
  useClocContext,
  ClocTrackingHeatmap,
  TrackingProvider
} from '@cloc/atoms';
import { useState, useEffect } from 'react';

function RobustTimerComponent() {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const {
    isRunning,
    startTimer,
    stopTimer,
    loadings
  } = useClocContext();

  useEffect(() => {
    // Simulate initialization
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleTimerAction = async () => {
    try {
      setError(null);
      if (isRunning) {
        await stopTimer();
      } else {
        await startTimer();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  if (isLoading || loadings.timerStatusLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading timer...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-semibold">Error</h3>
        <p className="text-red-600">{error}</p>
        <button
          onClick={() => setError(null)}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <ModernCloc
        expanded={true}
        showProgress={true}
        variant="default"
      />

      <button
        onClick={handleTimerAction}
        disabled={loadings.timerStatusLoading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loadings.timerStatusLoading ? 'Processing...' : (isRunning ? 'Stop' : 'Start')}
      </button>
    </div>
  );
}

export default function AppWithErrorHandling() {
  return (
    <TrackingProvider>
      <ClocProvider>
        <div className="max-w-4xl mx-auto p-6">
          <h1 className="text-2xl font-bold mb-6">Robust Timer App</h1>
          <RobustTimerComponent />
        </div>
      </ClocProvider>
    </TrackingProvider>
  );
}
```

## Best Practices

### Performance Optimization

Optimize your Cloc implementation for better performance:

```tsx
import {
  ModernCloc,
  ClocProvider,
  useClocContext
} from '@cloc/atoms';
import { memo, useMemo, useCallback } from 'react';

// Memoize expensive components
const MemoizedTimer = memo(ModernCloc);

// Optimize context usage
const OptimizedTimerDisplay = memo(() => {
  const { hours, minutes, seconds, isRunning } = useClocContext();

  // Memoize formatted time to prevent unnecessary re-renders
  const formattedTime = useMemo(() => {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }, [hours, minutes, seconds]);

  return (
    <div className="font-mono text-2xl">
      {formattedTime}
      <span className={`ml-2 ${isRunning ? 'text-green-500' : 'text-red-500'}`}>
        {isRunning ? '●' : '○'}
      </span>
    </div>
  );
});

function PerformantApp() {
  // Memoize callbacks to prevent unnecessary re-renders
  const handleTimerComplete = useCallback(() => {
    console.log('Timer completed');
  }, []);

  return (
    <ClocProvider>
      <div className="p-6 space-y-6">
        <OptimizedTimerDisplay />

        <MemoizedTimer
          expanded={true}
          showProgress={true}
          variant="default"
          size="default"
        />
      </div>
    </ClocProvider>
  );
}
```

### Security Considerations

Implement secure practices for token management:

```tsx
import { tracker } from '@cloc/tracking';
import { ClocProvider } from '@cloc/atoms';
import { useEffect, useState } from 'react';

function SecureApp() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Secure token retrieval
    const initializeSecurely = async () => {
      try {
        // Get token from secure storage (not localStorage for sensitive data)
        const token = await getSecureToken();

        if (token) {
          tracker.start({
            organizationId: process.env.NEXT_PUBLIC_CLOC_ORG_ID!,
            tenantId: process.env.NEXT_PUBLIC_CLOC_TENANT_ID!,
            token: token
          });
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Authentication failed:', error);
      }
    };

    initializeSecurely();
  }, []);

  // Secure token retrieval function
  const getSecureToken = async (): Promise<string | null> => {
    // Implement your secure token retrieval logic
    // This could be from httpOnly cookies, secure storage, etc.
    const response = await fetch('/api/auth/token', {
      credentials: 'include'
    });

    if (response.ok) {
      const data = await response.json();
      return data.token;
    }

    return null;
  };

  if (!isAuthenticated) {
    return (
      <div className="p-6 text-center">
        <p>Authenticating...</p>
      </div>
    );
  }

  return (
    <ClocProvider>
      {/* Your authenticated app content */}
    </ClocProvider>
  );
}
```

:::tip Next Steps
- Explore the [API Reference](/docs/api-reference) for detailed component documentation
- Check out [Advanced Configuration](/docs/advanced-config) for customization options
- Visit [Troubleshooting](/docs/troubleshooting) if you encounter issues
- Join our [Community Discord](https://discord.gg/cloc) for support and discussions
:::

:::warning Important Notes
- Always use environment variables for sensitive configuration
- Implement proper error boundaries in production applications
- Test timer functionality across different browsers and devices
- Monitor performance with React DevTools when using multiple timers
:::

## Summary

This guide covered:

- ✅  **Basic Setup**: ClocProvider configuration and simple timer usage 
- ✅  **Timer Components**: ModernCloc, ClocBasic, and BasicTimer variations 
- ✅  **Analytics Integration**: Tracking setup and analytics components
- ✅  **Data Visualization**: Charts and reports with BasicClocReport
- ✅  **Real-World Examples**: E-commerce, analytics, A/B testing scenarios
- ✅  **Framework Integration**: Next.js, Builder.io, and React Native
- ✅  **Advanced Patterns**: Custom hooks, TypeScript, and error handling
- ✅  **Best Practices**: Performance optimization and security considerations

The Cloc SDK provides a comprehensive solution for time tracking and user analytics. Start with the basic examples and gradually incorporate more advanced features as your application grows.
